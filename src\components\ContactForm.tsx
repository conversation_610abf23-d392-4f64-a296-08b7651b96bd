import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { motion } from 'framer-motion';
import { Send, CheckCircle, AlertCircle, Wifi, WifiOff } from 'lucide-react';
import { GoogleReCaptchaProvider, useGoogleReCaptcha } from 'react-google-recaptcha-v3';
import { supabase } from '../lib/supabaseClient';
import { useLanguage } from '../contexts/LanguageContext';

type ContactFormData = {
  name: string;
  email: string;
  phone: string;
  message: string;
};

type ContactSubmission = {
  id?: string;
  name: string;
  email: string;
  phone: string;
  message: string;
  recaptcha_score?: number;
  created_at?: string;
};

const ContactFormInner = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');
  const [connectionStatus, setConnectionStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking');
  const { executeRecaptcha } = useGoogleReCaptcha();
  const { t } = useLanguage();

  // Test database connection on component mount
  useEffect(() => {
    const testConnection = async () => {
      try {
        const { error } = await supabase.from('contact_submissions').select('count').limit(1);
        if (error) {
          console.error('Connection test failed:', error);
          setConnectionStatus('disconnected');
        } else {
          setConnectionStatus('connected');
        }
      } catch (error) {
        console.error('Connection test error:', error);
        setConnectionStatus('disconnected');
      }
    };

    testConnection();
  }, []);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<ContactFormData>();

  const onSubmit = async (data: ContactFormData) => {
    setIsSubmitting(true);
    setSubmitStatus('idle');
    setErrorMessage('');

    try {
      let recaptchaToken = '';
      let recaptchaScore = 0;

      // Execute reCAPTCHA if available
      if (executeRecaptcha) {
        try {
          recaptchaToken = await executeRecaptcha('contact_form');
          // For now, we'll assume a good score since we can't verify server-side
          recaptchaScore = 0.8;
        } catch (recaptchaError) {
          console.warn('reCAPTCHA execution failed:', recaptchaError);
          // Continue without reCAPTCHA if it fails
        }
      }

      // Submit to Supabase
      const submissionData: ContactSubmission = {
        ...data,
        recaptcha_score: recaptchaScore,
        created_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from('contact_submissions')
        .insert([submissionData]);

      if (error) {
        console.error('Supabase error:', error);
        throw new Error(`Database error: ${error.message}`);
      }

      setSubmitStatus('success');
      reset();
    } catch (error) {
      console.error('Contact form submission error:', error);

      // Provide more specific error messages
      let errorMsg = 'An error occurred. Please try again.';

      if (error instanceof Error) {
        if (error.message.includes('Failed to fetch')) {
          errorMsg = 'Network error. Please check your internet connection and try again.';
        } else if (error.message.includes('Database error')) {
          errorMsg = 'Database connection error. Please try again later.';
        } else {
          errorMsg = error.message;
        }
      }

      setErrorMessage(errorMsg);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <motion.div
      className="bg-white rounded-lg shadow-lg p-8"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5 }}
    >
      <div className="mb-6">
        <div className="flex justify-between items-start mb-2">
          <h2 className="text-3xl font-bold text-coffee">{t('contact.form.title')}</h2>
          
        </div>
        <p className="text-gray-600">{t('contact.form.subtitle')}</p>
      </div>

      {submitStatus === 'success' && (
        <motion.div
          className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
          <p className="text-green-800">{t('contact.form.success')}</p>
        </motion.div>
      )}

      {submitStatus === 'error' && (
        <motion.div
          className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <AlertCircle className="h-5 w-5 text-red-600 mr-3" />
          <p className="text-red-800">{errorMessage || t('contact.form.error')}</p>
        </motion.div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="form-label">{t('contact.form.name')}</label>
            <input
              {...register('name', { 
                required: t('contact.form.nameRequired'),
                minLength: { value: 2, message: t('contact.form.nameMinLength') }
              })}
              className="form-input"
              placeholder={t('contact.form.namePlaceholder')}
            />
            {errors.name && (
              <p className="text-red-600 text-sm mt-1">{errors.name.message}</p>
            )}
          </div>

          <div>
            <label className="form-label">{t('contact.form.email')}</label>
            <input
              type="email"
              {...register('email', { 
                required: t('contact.form.emailRequired'),
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: t('contact.form.emailInvalid')
                }
              })}
              className="form-input"
              placeholder={t('contact.form.emailPlaceholder')}
            />
            {errors.email && (
              <p className="text-red-600 text-sm mt-1">{errors.email.message}</p>
            )}
          </div>
        </div>

        <div>
          <label className="form-label">{t('contact.form.phone')}</label>
          <input
            type="tel"
            {...register('phone', { 
              required: t('contact.form.phoneRequired'),
              pattern: {
                value: /^[\+]?[0-9\s\-\(\)]{10,}$/,
                message: t('contact.form.phoneInvalid')
              }
            })}
            className="form-input"
            placeholder={t('contact.form.phonePlaceholder')}
          />
          {errors.phone && (
            <p className="text-red-600 text-sm mt-1">{errors.phone.message}</p>
          )}
        </div>

        <div>
          <label className="form-label">{t('contact.form.message')}</label>
          <textarea
            {...register('message', { 
              required: t('contact.form.messageRequired'),
              minLength: { value: 10, message: t('contact.form.messageMinLength') }
            })}
            className="form-input h-32 resize-none"
            placeholder={t('contact.form.messagePlaceholder')}
          />
          {errors.message && (
            <p className="text-red-600 text-sm mt-1">{errors.message.message}</p>
          )}
        </div>

        {connectionStatus === 'disconnected' && (
          <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg flex items-center">
            <AlertCircle className="h-5 w-5 text-yellow-600 mr-3" />
            <p className="text-yellow-800">
              Database connection issue detected. The form may not work properly. Please try again later.
            </p>
          </div>
        )}

        <div className="text-center">
          <button
            type="submit"
            disabled={isSubmitting || connectionStatus === 'disconnected'}
            className={`
              btn btn-primary flex items-center justify-center mx-auto
              ${(isSubmitting || connectionStatus === 'disconnected') ? 'opacity-50 cursor-not-allowed' : ''}
            `}
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                {t('contact.form.sending')}
              </>
            ) : (
              <>
                <Send className="h-5 w-5 mr-2" />
                {connectionStatus === 'disconnected' ? 'Connection Issue' : t('contact.form.send')}
              </>
            )}
          </button>
        </div>

        <div className="text-center text-sm text-gray-500">
          {t('contact.form.recaptcha')}
        </div>
      </form>
    </motion.div>
  );
};

const ContactForm = () => {
  const recaptchaSiteKey = import.meta.env.VITE_RECAPTCHA_SITE_KEY;

  // If no reCAPTCHA key, show form without reCAPTCHA
  if (!recaptchaSiteKey) {
    return <ContactFormInner />;
  }

  return (
    <GoogleReCaptchaProvider reCaptchaKey={recaptchaSiteKey}>
      <ContactFormInner />
    </GoogleReCaptchaProvider>
  );
};

export default ContactForm;
