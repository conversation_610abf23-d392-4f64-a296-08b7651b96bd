import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { motion } from 'framer-motion';
import { Settings as SettingsIcon, Key, Users, Eye, EyeOff, Plus, Trash2, Shield, Edit, X } from 'lucide-react';
import { supabase } from '../../lib/supabaseClient';
import { supabaseAdmin } from '../../lib/supabaseAdmin';
import AdminLayout from '../../components/admin/AdminLayout';
import { useAdminLanguage } from '../../contexts/AdminLanguageContext';
import { useUserRole } from '../../hooks/useUserRole';

type PasswordFormData = {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
};

type UserFormData = {
  email: string;
  password: string;
  role: 'admin' | 'editor';
};

type AdminUser = {
  id: string;
  email: string;
  role: string;
  created_at: string;
  last_sign_in_at?: string;
  user_metadata?: {
    role?: string;
  };
};

const Settings = () => {
  const [activeTab, setActiveTab] = useState<'password' | 'users'>('password');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const { t } = useAdminLanguage();
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
    userPassword: false
  });
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [usersLoading, setUsersLoading] = useState(false);
  const [editingRole, setEditingRole] = useState<string | null>(null);
  const { isAdmin } = useUserRole();

  const passwordForm = useForm<PasswordFormData>();
  const userForm = useForm<UserFormData>();

  useEffect(() => {
    getCurrentUser();
    fetchUsers();
  }, []);

  const getCurrentUser = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    setCurrentUser(user);
  };

  const fetchUsers = async () => {
    setUsersLoading(true);
    try {
      const { data, error } = await supabaseAdmin.auth.admin.listUsers();
      if (error) throw error;

      // Process users to extract role from user_metadata
      const processedUsers = data.users.map(user => ({
        ...user,
        role: user.user_metadata?.role || 'admin' // Default to admin if no role set
      })) as AdminUser[];

      setUsers(processedUsers);
    } catch (error) {
      console.error('Error fetching users:', error);
      setMessage({ type: 'error', text: 'Failed to load users. Please check your admin permissions.' });
    } finally {
      setUsersLoading(false);
    }
  };

  const validatePassword = (password: string) => {
    if (password.length < 8) return 'Password must be at least 8 characters long';
    if (!/(?=.*[a-z])/.test(password)) return 'Password must contain at least one lowercase letter';
    if (!/(?=.*[A-Z])/.test(password)) return 'Password must contain at least one uppercase letter';
    if (!/(?=.*\d)/.test(password)) return 'Password must contain at least one number';
    return null;
  };

  const handlePasswordChange = async (data: PasswordFormData) => {
    setLoading(true);
    setMessage(null);

    try {
      // Validate new password
      const passwordError = validatePassword(data.newPassword);
      if (passwordError) {
        throw new Error(passwordError);
      }

      // Check if passwords match
      if (data.newPassword !== data.confirmPassword) {
        throw new Error('New passwords do not match');
      }

      // Update password
      const { error } = await supabase.auth.updateUser({
        password: data.newPassword
      });

      if (error) throw error;

      setMessage({ type: 'success', text: 'Password updated successfully!' });
      passwordForm.reset();
    } catch (error: any) {
      setMessage({ type: 'error', text: error.message || 'Failed to update password' });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateUser = async (data: UserFormData) => {
    setLoading(true);
    setMessage(null);

    try {
      // Validate password
      const passwordError = validatePassword(data.password);
      if (passwordError) {
        throw new Error(passwordError);
      }

      // Create user
      const { error } = await supabaseAdmin.auth.admin.createUser({
        email: data.email,
        password: data.password,
        email_confirm: true,
        user_metadata: { role: data.role }
      });

      if (error) throw error;

      setMessage({ type: 'success', text: 'User created successfully!' });
      userForm.reset();
      fetchUsers();
    } catch (error: any) {
      setMessage({ type: 'error', text: error.message || 'Failed to create user' });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (!window.confirm('Are you sure you want to delete this user?')) return;

    try {
      const { error } = await supabaseAdmin.auth.admin.deleteUser(userId);
      if (error) throw error;

      setMessage({ type: 'success', text: 'User deleted successfully!' });
      fetchUsers();
    } catch (error: any) {
      setMessage({ type: 'error', text: error.message || 'Failed to delete user' });
    }
  };

  const handleUpdateUserRole = async (userId: string, newRole: 'admin' | 'editor') => {
    try {
      const { error } = await supabaseAdmin.auth.admin.updateUserById(userId, {
        user_metadata: { role: newRole }
      });

      if (error) throw error;

      setMessage({ type: 'success', text: 'User role updated successfully!' });
      setEditingRole(null);
      fetchUsers();
    } catch (error: any) {
      setMessage({ type: 'error', text: error.message || 'Failed to update user role' });
    }
  };

  // Migration function to set roles for existing users
  const migrateExistingUsers = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabaseAdmin.auth.admin.listUsers();
      if (error) throw error;

      const updates = [];
      for (const user of data.users) {
        if (!user.user_metadata?.role) {
          // Set existing users as admin by default
          updates.push(
            supabaseAdmin.auth.admin.updateUserById(user.id, {
              user_metadata: { ...user.user_metadata, role: 'admin' }
            })
          );
        }
      }

      await Promise.all(updates);
      setMessage({ type: 'success', text: `Updated ${updates.length} users with default admin role` });
      fetchUsers();
    } catch (error: any) {
      setMessage({ type: 'error', text: error.message || 'Failed to migrate users' });
    } finally {
      setLoading(false);
    }
  };

  const togglePasswordVisibility = (field: keyof typeof showPasswords) => {
    setShowPasswords(prev => ({ ...prev, [field]: !prev[field] }));
  };

  const getUserRole = (user: AdminUser): string => {
    return user.user_metadata?.role || user.role || 'admin';
  };

  const getRoleDisplayName = (role: string): string => {
    switch (role) {
      case 'admin':
        return t('admin.admin');
      case 'editor':
        return t('admin.editor');
      default:
        return role;
    }
  };

  return (
    <AdminLayout>
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2 flex items-center">
          <SettingsIcon className="h-8 w-8 mr-3" />
          {t('admin.settings')}
        </h1>
        <p className="text-gray-600">{t('admin.settings.desc')}</p>
      </div>

      {message && (
        <motion.div
          className={`mb-6 p-4 rounded-lg ${
            message.type === 'success' 
              ? 'bg-green-50 border border-green-200 text-green-800' 
              : 'bg-red-50 border border-red-200 text-red-800'
          }`}
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {message.text}
        </motion.div>
      )}

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="border-b border-gray-200">
          <nav className="flex">
            <button
              onClick={() => setActiveTab('password')}
              className={`px-6 py-4 text-sm font-medium flex items-center ${
                activeTab === 'password'
                  ? 'border-b-2 border-coffee text-coffee bg-coffee/5'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <Key className="h-5 w-5 mr-2" />
              {t('admin.changePassword')}
            </button>
            {isAdmin() && (
              <button
                onClick={() => setActiveTab('users')}
                className={`px-6 py-4 text-sm font-medium flex items-center ${
                  activeTab === 'users'
                    ? 'border-b-2 border-coffee text-coffee bg-coffee/5'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <Users className="h-5 w-5 mr-2" />
                {t('admin.userManagement')}
              </button>
            )}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'password' && (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
            >
              <h3 className="text-lg font-semibold mb-4">{t('admin.changePassword')}</h3>
              <form onSubmit={passwordForm.handleSubmit(handlePasswordChange)} className="space-y-6 max-w-md">
                <div>
                  <label className="form-label">{t('admin.currentPassword')}</label>
                  <div className="relative">
                    <input
                      type={showPasswords.current ? 'text' : 'password'}
                      {...passwordForm.register('currentPassword', { required: 'Current password is required' })}
                      className="form-input pr-10"
                      placeholder={t('admin.currentPasswordPlaceholder')}
                    />
                    <button
                      type="button"
                      onClick={() => togglePasswordVisibility('current')}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                    >
                      {showPasswords.current ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    </button>
                  </div>
                  {passwordForm.formState.errors.currentPassword && (
                    <p className="text-red-600 text-sm mt-1">{passwordForm.formState.errors.currentPassword.message}</p>
                  )}
                </div>

                <div>
                  <label className="form-label">{t('admin.newPassword')}</label>
                  <div className="relative">
                    <input
                      type={showPasswords.new ? 'text' : 'password'}
                      {...passwordForm.register('newPassword', { required: 'New password is required' })}
                      className="form-input pr-10"
                      placeholder={t('admin.newPasswordPlaceholder')}
                    />
                    <button
                      type="button"
                      onClick={() => togglePasswordVisibility('new')}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                    >
                      {showPasswords.new ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    </button>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Password must be at least 8 characters with uppercase, lowercase, and number
                  </p>
                </div>

                <div>
                  <label className="form-label">{t('admin.confirmPassword')}</label>
                  <div className="relative">
                    <input
                      type={showPasswords.confirm ? 'text' : 'password'}
                      {...passwordForm.register('confirmPassword', { required: 'Please confirm your password' })}
                      className="form-input pr-10"
                      placeholder={t('admin.confirmPasswordPlaceholder')}
                    />
                    <button
                      type="button"
                      onClick={() => togglePasswordVisibility('confirm')}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                    >
                      {showPasswords.confirm ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    </button>
                  </div>
                </div>

                <button
                  type="submit"
                  disabled={loading}
                  className="btn btn-primary flex items-center"
                >
                  {loading ? 'Updating...' : t('admin.updatePassword')}
                </button>
              </form>
            </motion.div>
          )}

          {activeTab === 'users' && isAdmin() && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="mb-8">
                <h3 className="text-lg font-semibold mb-4">{t('admin.createUser')}</h3>
                <form onSubmit={userForm.handleSubmit(handleCreateUser)} className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <label className="form-label">{t('admin.email')}</label>
                    <input
                      type="email"
                      {...userForm.register('email', { required: 'Email is required' })}
                      className="form-input"
                      placeholder={t('admin.emailPlaceholder')}
                    />
                  </div>
                  <div>
                    <label className="form-label">{t('admin.password')}</label>
                    <div className="relative">
                      <input
                        type={showPasswords.userPassword ? 'text' : 'password'}
                        {...userForm.register('password', { required: 'Password is required' })}
                        className="form-input pr-10"
                        placeholder={t('admin.passwordPlaceholder')}
                      />
                      <button
                        type="button"
                        onClick={() => togglePasswordVisibility('userPassword')}
                        className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                      >
                        {showPasswords.userPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                      </button>
                    </div>
                  </div>
                  <div>
                    <label className="form-label">{t('admin.role')}</label>
                    <select {...userForm.register('role')} className="form-input">
                      <option value="admin">{t('admin.admin')}</option>
                      <option value="editor">{t('admin.editor')}</option>
                    </select>
                  </div>
                  <div className="flex items-end">
                    <button
                      type="submit"
                      disabled={loading}
                      className="btn btn-primary flex items-center w-full"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      {loading ? 'Creating...' : t('admin.createUser')}
                    </button>
                  </div>
                </form>
              </div>

              <div>
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold">{t('admin.existingUsers')}</h3>
                  <button
                    onClick={migrateExistingUsers}
                    disabled={loading}
                    className="btn btn-secondary text-sm"
                    title="Set admin role for users without roles"
                  >
                    {loading ? 'Migrating...' : 'Fix User Roles'}
                  </button>
                </div>
                {usersLoading ? (
                  <div className="text-center py-8">
                    <p className="text-gray-500">Loading users...</p>
                  </div>
                ) : users.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-gray-500">No users found.</p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {t('admin.user')}
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {t('admin.role')}
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {t('admin.lastSignIn')}
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {t('admin.actions')}
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {users.map((user) => (
                          <tr key={user.id}>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <Shield className="h-5 w-5 text-gray-400 mr-3" />
                                <div>
                                  <div className="text-sm font-medium text-gray-900">{user.email}</div>
                                  <div className="text-sm text-gray-500">ID: {user.id.slice(0, 8)}...</div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              {editingRole === user.id ? (
                                <div className="flex items-center space-x-2">
                                  <select
                                    defaultValue={getUserRole(user)}
                                    onChange={(e) => handleUpdateUserRole(user.id, e.target.value as 'admin' | 'editor')}
                                    className="text-xs border border-gray-300 rounded px-2 py-1"
                                  >
                                    <option value="admin">{t('admin.admin')}</option>
                                    <option value="editor">{t('admin.editor')}</option>
                                  </select>
                                  <button
                                    onClick={() => setEditingRole(null)}
                                    className="text-gray-500 hover:text-gray-700"
                                  >
                                    <X className="h-4 w-4" />
                                  </button>
                                </div>
                              ) : (
                                <div className="flex items-center space-x-2">
                                  <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-coffee/10 text-coffee">
                                    {getRoleDisplayName(getUserRole(user))}
                                  </span>
                                  {user.id !== currentUser?.id && (
                                    <button
                                      onClick={() => setEditingRole(user.id)}
                                      className="text-blue-600 hover:text-blue-800"
                                      title={t('admin.editRole')}
                                    >
                                      <Edit className="h-4 w-4" />
                                    </button>
                                  )}
                                </div>
                              )}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {user.last_sign_in_at
                                ? new Date(user.last_sign_in_at).toLocaleDateString()
                                : t('admin.never')
                              }
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              {user.id !== currentUser?.id && (
                                <button
                                  onClick={() => handleDeleteUser(user.id)}
                                  className="text-red-600 hover:text-red-900 flex items-center"
                                >
                                  <Trash2 className="h-4 w-4 mr-1" />
                                  {t('admin.delete')}
                                </button>
                              )}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
};

export default Settings;
