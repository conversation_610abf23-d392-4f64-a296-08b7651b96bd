import { useState, useEffect } from 'react';
import { Routes, Route, useLocation } from 'react-router-dom';
import { Session } from '@supabase/supabase-js';
import { supabase } from './lib/supabaseClient';

// Components
import Navbar from './components/layout/Navbar';
import Footer from './components/layout/Footer';

// Pages
import Home from './pages/Home';
import Menu from './pages/Menu';
import Blog from './pages/Blog';
import BlogPost from './pages/BlogPost';
import Login from './pages/Login';
import ResetPassword from './pages/ResetPassword';
import NotFound from './pages/NotFound';
import PrivacyPolicy from './pages/PrivacyPolicy';
import TermsOfService from './pages/TermsOfService';

// Admin Pages
import AdminDashboard from './pages/admin/Dashboard';
import AdminMenu from './pages/admin/Menu';
import AdminBlog from './pages/admin/Blog';
import AdminContent from './pages/admin/Content';
import ContactSubmissions from './pages/admin/ContactSubmissions';
import Settings from './pages/admin/Settings';

// Context
import { AuthProvider } from './context/AuthContext';
import { LanguageProvider } from './contexts/LanguageContext';
import { AdminLanguageProvider } from './contexts/AdminLanguageContext';

// Components
import ProtectedRoute from './components/admin/ProtectedRoute';

function App() {
  const [session, setSession] = useState<Session | null>(null);

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
    });

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
    });

    return () => subscription.unsubscribe();
  }, []);

  return (
    <LanguageProvider>
      <AuthProvider value={{ session }}>
        <AppContent session={session} />
      </AuthProvider>
    </LanguageProvider>
  );
}

// Separate component to access useLocation hook
function AppContent({ session }: { session: Session | null }) {
  const location = useLocation();
  const isAdminRoute = location.pathname.startsWith('/admin');

  return (
    <div className="flex flex-col min-h-screen bg-cream">
      {!isAdminRoute && <Navbar />}
      <main className={isAdminRoute ? '' : 'flex-grow'}>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/menu" element={<Menu />} />
          <Route path="/blog" element={<Blog />} />
          <Route path="/blog/:id" element={<BlogPost />} />
          <Route path="/privacy-policy" element={<PrivacyPolicy />} />
          <Route path="/terms-of-service" element={<TermsOfService />} />
          <Route path="/login" element={<Login />} />
          <Route path="/admin/reset-password" element={<ResetPassword />} />

          {/* Admin Routes - Protected */}
          <Route
            path="/admin"
            element={
              <AdminLanguageProvider>
                <ProtectedRoute>
                  <AdminDashboard />
                </ProtectedRoute>
              </AdminLanguageProvider>
            }
          />
          <Route
            path="/admin/menu"
            element={
              <AdminLanguageProvider>
                <ProtectedRoute requiredPermission="canManageMenu">
                  <AdminMenu />
                </ProtectedRoute>
              </AdminLanguageProvider>
            }
          />
          <Route
            path="/admin/blog"
            element={
              <AdminLanguageProvider>
                <ProtectedRoute requiredPermission="canManageBlog">
                  <AdminBlog />
                </ProtectedRoute>
              </AdminLanguageProvider>
            }
          />
          <Route
            path="/admin/content"
            element={
              <AdminLanguageProvider>
                <ProtectedRoute requiredPermission="canManageContent">
                  <AdminContent />
                </ProtectedRoute>
              </AdminLanguageProvider>
            }
          />
          <Route
            path="/admin/contact-submissions"
            element={
              <AdminLanguageProvider>
                <ProtectedRoute requiredPermission="canViewContactSubmissions">
                  <ContactSubmissions />
                </ProtectedRoute>
              </AdminLanguageProvider>
            }
          />
          <Route
            path="/admin/settings"
            element={
              <AdminLanguageProvider>
                <ProtectedRoute adminOnly={true}>
                  <Settings />
                </ProtectedRoute>
              </AdminLanguageProvider>
            }
          />

          {/* 404 Route */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </main>
      {!isAdminRoute && <Footer />}
    </div>
  );
}

export default App;