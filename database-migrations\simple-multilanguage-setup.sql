-- Simple Multi-Language Setup - Run each section separately if needed
-- Copy and paste each section one by one into Supabase SQL Editor

-- SECTION 1: Add language columns to blog_posts
-- Run this first:
ALTER TABLE blog_posts ADD COLUMN IF NOT EXISTS language VARCHAR(2) DEFAULT 'tr';
ALTER TABLE blog_posts ADD COLUMN IF NOT EXISTS title_en TEXT;
ALTER TABLE blog_posts ADD COLUMN IF NOT EXISTS excerpt_en TEXT;
ALTER TABLE blog_posts ADD COLUMN IF NOT EXISTS content_en TEXT;

-- SECTION 2: Add language columns to menu_items
-- Run this second:
ALTER TABLE menu_items ADD COLUMN IF NOT EXISTS language VARCHAR(2) DEFAULT 'tr';
ALTER TABLE menu_items ADD COLUMN IF NOT EXISTS name_en TEXT;
ALTER TABLE menu_items ADD COLUMN IF NOT EXISTS description_en TEXT;

-- SECTION 3: Create content table (if it doesn't exist)
-- Run this third:
CREATE TABLE IF NOT EXISTS content (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    image_url TEXT,
    display_order INTEGER DEFAULT 0,
    section VARCHAR(255) NOT NULL,
    page VARCHAR(255) NOT NULL,
    language VARCHAR(2) DEFAULT 'tr',
    title_en TEXT,
    content_en TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- SECTION 4: Update existing data
-- Run this fourth:
UPDATE blog_posts SET language = 'tr' WHERE language IS NULL;
UPDATE menu_items SET language = 'tr' WHERE language IS NULL;
UPDATE content SET language = 'tr' WHERE language IS NULL;

-- SECTION 5: Copy content to English fields
-- Run this fifth:
UPDATE blog_posts SET 
    title_en = title,
    excerpt_en = excerpt,
    content_en = content
WHERE title_en IS NULL AND title IS NOT NULL;

UPDATE menu_items SET 
    name_en = name,
    description_en = description
WHERE name_en IS NULL AND name IS NOT NULL;

UPDATE content SET 
    title_en = title,
    content_en = content
WHERE title_en IS NULL AND title IS NOT NULL;

-- SECTION 6: Create indexes
-- Run this last:
CREATE INDEX IF NOT EXISTS idx_blog_posts_language ON blog_posts(language);
CREATE INDEX IF NOT EXISTS idx_menu_items_language ON menu_items(language);
CREATE INDEX IF NOT EXISTS idx_content_language ON content(language);

-- SECTION 7: Check results
-- Run this to see what happened:
SELECT 'blog_posts' as table_name, COUNT(*) as total_rows FROM blog_posts
UNION ALL
SELECT 'menu_items' as table_name, COUNT(*) as total_rows FROM menu_items
UNION ALL
SELECT 'content' as table_name, COUNT(*) as total_rows FROM content;
