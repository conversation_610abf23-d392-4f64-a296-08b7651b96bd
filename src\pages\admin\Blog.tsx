import { useState, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { motion } from 'framer-motion';
import { Plus, Edit, Trash2, X, Search, Calendar, Eye, EyeOff } from 'lucide-react';
import { format } from 'date-fns';
import { supabase, BlogPost } from '../../lib/supabaseClient';
import AdminLayout from '../../components/admin/AdminLayout';
import RichTextEditor from '../../components/admin/RichTextEditor';
import MultiLanguageForm from '../../components/admin/MultiLanguageForm';
import { useAdminLanguage } from '../../contexts/AdminLanguageContext';
import { useUserRole } from '../../hooks/useUserRole';
import { createBlogSlug } from '../../utils/slugify';

type FormData = Omit<BlogPost, 'id' | 'created_at'>;

const AdminBlog = () => {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [filteredPosts, setFilteredPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [currentPost, setCurrentPost] = useState<BlogPost | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [categories, setCategories] = useState<string[]>([]);
  const [showForm, setShowForm] = useState(false);

  // Multi-language state
  const [primaryLanguage, setPrimaryLanguage] = useState<'tr' | 'en'>('tr');
  const [multiLangTitle, setMultiLangTitle] = useState({ tr: '', en: '' });
  const [multiLangExcerpt, setMultiLangExcerpt] = useState({ tr: '', en: '' });
  const [multiLangContent, setMultiLangContent] = useState({ tr: '', en: '' });

  const { register, handleSubmit, reset, setValue, control, formState: { errors } } = useForm<FormData>();
  const { t } = useAdminLanguage();
  const { hasPermission } = useUserRole();

  useEffect(() => {
    fetchBlogPosts();
  }, []);

  // Fetch blog posts
  const fetchBlogPosts = async () => {
    setLoading(true);

    try {
      const { data, error } = await supabase
        .from('blog_posts')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      if (data) {
        setPosts(data);

        // Extract unique categories
        const uniqueCategories = [...new Set(data.map(post => post.category))];
        setCategories(uniqueCategories);
      }
    } catch (error) {
      console.error('Error fetching blog posts:', error);

      // Use default data if database fetch fails
      const defaultPosts: BlogPost[] = [
        {
          id: '1',
          title: 'The Secret to Our Juicy Burgers',
          content: `<p>At Shebo's Burger, we take pride in serving the juiciest, most flavorful burgers in town. But what's our secret? It all starts with quality ingredients.</p><p>We use only premium beef that's ground fresh daily. Our special blend of chuck and brisket provides the perfect fat content for a juicy, flavorful patty. We season our patties with a proprietary mix of spices that enhances the natural flavor of the beef without overwhelming it.</p><p>Another key factor is how we cook our burgers. We use a flat-top grill that sears the patties perfectly, locking in juices and creating a delicious crust. We always cook to order, ensuring each burger reaches your table at peak deliciousness.</p><p>Finally, we pay attention to the details. Our buns are baked fresh daily, and we toast them lightly for the perfect texture. Our toppings are always fresh and high-quality, from crisp lettuce to vine-ripened tomatoes.</p><p>Come taste the difference at Shebo's Burger!</p>`,
          excerpt: 'Discover what makes our burgers stand out from the competition - from ingredient selection to cooking techniques.',
          image_url: 'https://images.pexels.com/photos/3219547/pexels-photo-3219547.jpeg',
          author: 'Chef Michael',
          category: 'Cooking Tips',
          published: true,
          created_at: '2023-06-15T12:00:00Z'
        },
        {
          id: '2',
          title: 'Upcoming Summer Special Menu',
          content: `<p>Summer is just around the corner, and we're excited to announce our new seasonal menu! We've created a selection of fresh, vibrant dishes that capture the essence of summer.</p><p>Our new Tropical Paradise Burger features a juicy beef patty topped with grilled pineapple, crispy bacon, and a zesty mango sauce. It's like a vacation in every bite! For seafood lovers, our Grilled Shrimp Burger with avocado and citrus slaw is a must-try.</p><p>We're also introducing some refreshing new sides. Our Watermelon Feta Salad is the perfect light accompaniment to any burger, and our Sweet Corn Fritters with chili-lime dip are already becoming a customer favorite.</p><p>To help you beat the heat, we've created new summer beverages including a Strawberry Basil Lemonade and a Coconut Cold Brew that are both refreshing and delicious.</p><p>Our summer menu launches on June 1st and will be available through August. Don't miss your chance to try these seasonal specialties!</p>`,
          excerpt: 'Get a sneak peek at our upcoming summer menu featuring seasonal ingredients and tropical flavors.',
          image_url: 'https://images.pexels.com/photos/1600711/pexels-photo-1600711.jpeg',
          author: 'Sarah Williams',
          category: 'Menu Updates',
          published: true,
          created_at: '2023-05-20T14:30:00Z'
        }
      ];

      setPosts(defaultPosts);

      // Extract unique categories
      const uniqueCategories = [...new Set(defaultPosts.map(post => post.category))];
      setCategories(uniqueCategories);
    } finally {
      setLoading(false);
    }
  };

  // Filter posts when search or posts change
  useEffect(() => {
    let result = posts;

    // Apply search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(
        post =>
          post.title.toLowerCase().includes(query) ||
          post.content.toLowerCase().includes(query) ||
          post.category.toLowerCase().includes(query) ||
          post.author.toLowerCase().includes(query)
      );
    }

    setFilteredPosts(result);
  }, [posts, searchQuery]);

  // Handle add/edit form submission
  const onSubmit = async (data: FormData) => {
    try {
      // Prepare data with multi-language content
      const submitData = {
        ...data,
        language: primaryLanguage,
        title: multiLangTitle[primaryLanguage],
        excerpt: multiLangExcerpt[primaryLanguage],
        content: multiLangContent[primaryLanguage],
        title_en: primaryLanguage === 'tr' ? multiLangTitle.en : undefined,
        excerpt_en: primaryLanguage === 'tr' ? multiLangExcerpt.en : undefined,
        content_en: primaryLanguage === 'tr' ? multiLangContent.en : undefined,
      };

      if (isEditing && currentPost) {
        // Update existing post
        const updateData = { ...submitData };

        // Generate new slug if title changed
        if (submitData.title !== currentPost.title) {
          const existingSlugs = posts
            .filter(p => p.id !== currentPost.id)
            .map(p => p.slug)
            .filter(Boolean) as string[];
          updateData.slug = createBlogSlug(submitData.title, existingSlugs);
        }

        const { error } = await supabase
          .from('blog_posts')
          .update(updateData)
          .eq('id', currentPost.id);

        if (error) throw error;

        // Update local state
        setPosts(prev =>
          prev.map(post =>
            post.id === currentPost.id
              ? { ...post, ...updateData }
              : post
          )
        );
      } else {
        // Add new post - generate slug
        const existingSlugs = posts.map(p => p.slug).filter(Boolean) as string[];
        const slug = createBlogSlug(submitData.title, existingSlugs);

        const { data: newPost, error } = await supabase
          .from('blog_posts')
          .insert([{
            ...submitData,
            slug,
            created_at: new Date().toISOString()
          }])
          .select();

        if (error) throw error;

        // Update local state
        if (newPost) {
          setPosts(prev => [...prev, ...newPost]);

          // Add new category if it doesn't exist
          if (!categories.includes(submitData.category)) {
            setCategories(prev => [...prev, submitData.category]);
          }
        }
      }

      // Reset form and close
      closeForm();
    } catch (error) {
      console.error('Error saving blog post:', error);
      alert('Failed to save blog post. Please try again.');
    }
  };

  // Delete blog post
  const handleDelete = async (id: string) => {
    if (!window.confirm(t('admin.blog.deleteConfirm'))) {
      return;
    }

    try {
      const { error } = await supabase
        .from('blog_posts')
        .delete()
        .eq('id', id);

      if (error) throw error;

      // Update local state
      setPosts(prev => prev.filter(post => post.id !== id));
    } catch (error) {
      console.error('Error deleting blog post:', error);
      alert('Failed to delete blog post. Please try again.');
    }
  };

  // Toggle post published status
  const togglePublished = async (post: BlogPost) => {
    try {
      const { error } = await supabase
        .from('blog_posts')
        .update({ published: !post.published })
        .eq('id', post.id);

      if (error) throw error;

      // Update local state
      setPosts(prev =>
        prev.map(item =>
          item.id === post.id
            ? { ...item, published: !item.published }
            : item
        )
      );
    } catch (error) {
      console.error('Error toggling post status:', error);
      alert('Failed to update post status. Please try again.');
    }
  };

  // Open form for editing
  const handleEdit = (post: BlogPost) => {
    setCurrentPost(post);
    setIsEditing(true);

    // Set primary language and multi-language content
    setPrimaryLanguage(post.language || 'tr');
    setMultiLangTitle({
      tr: post.title,
      en: post.title_en || ''
    });
    setMultiLangExcerpt({
      tr: post.excerpt,
      en: post.excerpt_en || ''
    });
    setMultiLangContent({
      tr: post.content,
      en: post.content_en || ''
    });

    // Set form values
    setValue('title', post.title);
    setValue('content', post.content);
    setValue('excerpt', post.excerpt);
    setValue('category', post.category);
    setValue('author', post.author);
    setValue('image_url', post.image_url || '');
    setValue('published', post.published);
    setValue('language', post.language || 'tr');
    setValue('title_en', post.title_en || '');
    setValue('excerpt_en', post.excerpt_en || '');
    setValue('content_en', post.content_en || '');

    setShowForm(true);
  };

  // Open form for adding
  const handleAdd = () => {
    setCurrentPost(null);
    setIsEditing(false);

    // Reset multi-language state
    setPrimaryLanguage('tr');
    setMultiLangTitle({ tr: '', en: '' });
    setMultiLangExcerpt({ tr: '', en: '' });
    setMultiLangContent({ tr: '', en: '' });

    // Reset form with defaults
    reset({
      title: '',
      content: '',
      excerpt: '',
      category: '',
      author: '',
      image_url: '',
      published: false,
      language: 'tr',
      title_en: '',
      excerpt_en: '',
      content_en: '',
    });

    setShowForm(true);
  };

  // Close form
  const closeForm = () => {
    setShowForm(false);
    setCurrentPost(null);
    setIsEditing(false);
    reset();
  };

  return (
    <AdminLayout>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold mb-2">{t('admin.blog.title')}</h1>
          <p className="text-gray-600">{t('admin.blog.subtitle')}</p>
        </div>

        <button
          onClick={handleAdd}
          className="btn bg-coffee text-white hover:bg-coffee/90 flex items-center"
        >
          <Plus className="h-5 w-5 mr-2" />
          {t('admin.blog.newPost')}
        </button>
      </div>

      {/* Search */}
      <div className="bg-white rounded-lg shadow-md p-4 mb-6">
        <div className="relative">
          <input
            type="text"
            placeholder={t('admin.blog.searchPlaceholder')}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-coffee"
          />
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
        </div>
      </div>

      {/* Blog Posts Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        {loading ? (
          <div className="p-6 text-center">
            <p>{t('admin.blog.loadingPosts')}</p>
          </div>
        ) : filteredPosts.length === 0 ? (
          <div className="p-6 text-center">
            <p>{t('admin.blog.noPostsFound')}</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 text-left">
                <tr>
                  <th className="px-6 py-3 text-coffee font-semibold">{t('admin.blog.title.column')}</th>
                  <th className="px-6 py-3 text-coffee font-semibold">{t('admin.blog.category')}</th>
                  <th className="px-6 py-3 text-coffee font-semibold">{t('admin.blog.author')}</th>
                  <th className="px-6 py-3 text-coffee font-semibold">{t('admin.blog.date')}</th>
                  <th className="px-6 py-3 text-coffee font-semibold">{t('admin.blog.status')}</th>
                  <th className="px-6 py-3 text-coffee font-semibold">{t('admin.blog.actions')}</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {filteredPosts.map((post) => (
                  <tr key={post.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        {post.image_url && (
                          <img
                            src={post.image_url}
                            alt={post.title}
                            className="h-10 w-10 rounded-md object-cover mr-3"
                          />
                        )}
                        <div>
                          <p className="font-medium">{post.title}</p>
                          <p className="text-sm text-gray-500 truncate max-w-xs">
                            {post.excerpt}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">{post.category}</td>
                    <td className="px-6 py-4">{post.author}</td>
                    <td className="px-6 py-4">
                      {format(new Date(post.created_at), 'MMM d, yyyy')}
                    </td>
                    <td className="px-6 py-4">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        post.published
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {post.published ? t('admin.blog.published') : t('admin.blog.draft')}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-3">
                        {hasPermission('canPublishContent') && (
                          <button
                            onClick={() => togglePublished(post)}
                            className={`${
                              post.published
                                ? 'text-yellow-600 hover:text-yellow-800'
                                : 'text-green-600 hover:text-green-800'
                            }`}
                            title={post.published ? t('admin.blog.unpublish') : t('admin.blog.publish')}
                          >
                            {post.published ? (
                              <EyeOff className="h-5 w-5" />
                            ) : (
                              <Eye className="h-5 w-5" />
                            )}
                          </button>
                        )}
                        <button
                          onClick={() => handleEdit(post)}
                          className="text-blue-600 hover:text-blue-800"
                          title={t('admin.edit')}
                        >
                          <Edit className="h-5 w-5" />
                        </button>
                        {hasPermission('canDeleteContent') && (
                          <button
                            onClick={() => handleDelete(post.id)}
                            className="text-red-600 hover:text-red-800"
                            title={t('admin.delete')}
                          >
                            <Trash2 className="h-5 w-5" />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Add/Edit Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[100] p-4 pt-20">
          <motion.div
            className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[85vh] overflow-y-auto relative"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex justify-between items-center p-6 border-b">
              <h2 className="text-2xl font-bold">
                {isEditing ? t('admin.blog.editPost') : t('admin.blog.createPost')}
              </h2>
              <button
                onClick={closeForm}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <form onSubmit={handleSubmit(onSubmit)} className="p-6">
              <div className="grid grid-cols-1 gap-6">
                {/* Multi-Language Content */}
                <MultiLanguageForm
                  title={multiLangTitle}
                  content={multiLangContent}
                  onTitleChange={(lang, value) => {
                    setMultiLangTitle(prev => ({ ...prev, [lang]: value }));
                    if (lang === primaryLanguage) {
                      setValue('title', value);
                    }
                  }}
                  onContentChange={(lang, value) => {
                    setMultiLangContent(prev => ({ ...prev, [lang]: value }));
                    if (lang === primaryLanguage) {
                      setValue('content', value);
                    }
                  }}
                  primaryLanguage={primaryLanguage}
                  onPrimaryLanguageChange={(lang) => {
                    setPrimaryLanguage(lang);
                    setValue('language', lang);
                    setValue('title', multiLangTitle[lang]);
                    setValue('content', multiLangContent[lang]);
                  }}
                  titleLabel="Blog Post Title"
                  contentLabel="Blog Post Content"
                  titlePlaceholder="Enter blog post title..."
                  contentPlaceholder="Write your blog post content here..."
                />

                {/* Excerpt Multi-Language */}
                <div>
                  <label className="form-label">{t('admin.blog.excerpt')} ({primaryLanguage === 'tr' ? 'Türkçe' : 'English'})</label>
                  <textarea
                    value={multiLangExcerpt[primaryLanguage]}
                    onChange={(e) => {
                      setMultiLangExcerpt(prev => ({ ...prev, [primaryLanguage]: e.target.value }));
                      setValue('excerpt', e.target.value);
                    }}
                    className="form-input h-20"
                    placeholder="A brief summary of the post (displayed in previews)"
                  />
                  {primaryLanguage === 'tr' && (
                    <div className="mt-2">
                      <label className="form-label text-sm">English Translation</label>
                      <textarea
                        value={multiLangExcerpt.en}
                        onChange={(e) => setMultiLangExcerpt(prev => ({ ...prev, en: e.target.value }))}
                        className="form-input h-16 text-sm"
                        placeholder="English excerpt translation..."
                      />
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="form-label">{t('admin.blog.category')}</label>
                    <input
                      {...register('category', { required: 'Category is required' })}
                      className="form-input"
                      placeholder="e.g., Cooking Tips, News, Events"
                      list="blog-categories"
                    />
                    <datalist id="blog-categories">
                      {categories.map((category) => (
                        <option key={category} value={category} />
                      ))}
                    </datalist>
                    {errors.category && (
                      <p className="text-red-600 text-sm mt-1">{errors.category.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="form-label">{t('admin.blog.author')}</label>
                    <input
                      {...register('author', { required: 'Author is required' })}
                      className="form-input"
                      placeholder="Author name"
                    />
                    {errors.author && (
                      <p className="text-red-600 text-sm mt-1">{errors.author.message}</p>
                    )}
                  </div>
                </div>

                <div>
                  <label className="form-label">{t('admin.blog.featuredImage')}</label>
                  <input
                    {...register('image_url')}
                    className="form-input"
                    placeholder="https://example.com/image.jpg"
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="published"
                    {...register('published')}
                    disabled={!hasPermission('canPublishContent')}
                    className="h-4 w-4 text-coffee focus:ring-coffee rounded disabled:opacity-50"
                  />
                  <label htmlFor="published" className="ml-2 text-coffee">
                    {t('admin.blog.publishImmediately')}
                    {!hasPermission('canPublishContent') && (
                      <span className="text-sm text-gray-500 ml-2">({t('admin.adminOnly')})</span>
                    )}
                  </label>
                </div>
              </div>

              <div className="mt-6 flex justify-end gap-3">
                <button
                  type="button"
                  onClick={closeForm}
                  className="btn bg-gray-200 text-gray-800 hover:bg-gray-300"
                >
                  {t('admin.cancel')}
                </button>
                <button
                  type="submit"
                  className="btn btn-primary"
                >
                  {isEditing ? t('admin.blog.updatePost') : t('admin.blog.createPost.button')}
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}
    </AdminLayout>
  );
};

export default AdminBlog;