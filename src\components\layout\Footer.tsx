import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Facebook, Instagram, Twitter, MapPin, Phone, Mail } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';
import { supabase, ContentSection } from '../../lib/supabaseClient';

const Footer = () => {
  const { t } = useLanguage();
  const currentYear = new Date().getFullYear();
  const [footerContent, setFooterContent] = useState<Record<string, ContentSection>>({});

  useEffect(() => {
    const fetchFooterContent = async () => {
      const { data, error } = await supabase
        .from('content')
        .select('*')
        .eq('page', 'global')
        .eq('section', 'footer');

      if (error) {
        console.error('Error fetching footer content:', error);
      } else if (data) {
        const contentMap: Record<string, ContentSection> = {};
        data.forEach((item: ContentSection) => {
          contentMap[item.name] = item;
        });
        setFooterContent(contentMap);
      }
    };

    fetchFooterContent();
  }, []);

  // Default content if database content is not available
  const getContent = (key: string, defaultValue: string) => {
    return footerContent[key]?.content || defaultValue;
  };

  return (
    <footer className="bg-coffee text-cream">
      <div className="container-custom py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* About */}
          <div>
            <h4 className="text-xl font-serif font-bold mb-4 text-cream">{t('footer.about')}</h4>
            <p className="mb-4">
              {getContent('footer-description', t('footer.description'))}
            </p>
            <div className="flex space-x-4">
              <SocialLink href="https://facebook.com/shebosburger" icon={<Facebook className="h-5 w-5" />} label="Facebook" />
              <SocialLink href="https://instagram.com/shebosburger" icon={<Instagram className="h-5 w-5" />} label="Instagram" />
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-xl font-serif font-bold mb-4 text-cream">{t('footer.links')}</h4>
            <ul className="space-y-2">
              <FooterLink to="/" label={t('nav.home')} />
              <FooterLink to="/menu" label={t('nav.menu')} />
              <FooterLink to="/blog" label={t('nav.blog')} />
              <FooterLink to="/login" label={t('admin.login')} />
            </ul>
          </div>

          {/* Opening Hours */}
          <div>
            <h4 className="text-xl font-serif font-bold mb-4 text-cream">{t('footer.hours')}</h4>
            <ul className="space-y-2">
              <li className="flex justify-between">
                <span>Pazartesi - Pazar: 11:30 - 21:45</span>
              </li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h4 className="text-xl font-serif font-bold mb-4 text-cream">{t('footer.contact')}</h4>
            <ul className="space-y-3">
              <li className="flex items-start">
                <MapPin className="h-5 w-5 mr-2 mt-1 flex-shrink-0" />
                <span>{getContent('footer-address', t('footer.address'))}</span>
              </li>
              <li className="flex items-center">
                <Phone className="h-5 w-5 mr-2 flex-shrink-0" />
                <span>{getContent('footer-phone', t('footer.phone'))}</span>
              </li>
              <li className="flex items-center">
                <Mail className="h-5 w-5 mr-2 flex-shrink-0" />
                <span>{getContent('footer-email', t('footer.email'))}</span>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-cream/20 mt-8 pt-6 flex flex-col md:flex-row justify-between items-center">
          <p>&copy; {currentYear} SHEBO'S Cafe & Burger - Bostanlı {t('footer.copyright')}</p>
          <div className="mt-4 md:mt-0">
            <Link to="/privacy-policy" className="text-cream/80 hover:text-cream mr-4">
              {t('footer.privacy')}
            </Link>
            <Link to="/terms-of-service" className="text-cream/80 hover:text-cream">
              {t('footer.terms')}
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

// Footer Link Component
const FooterLink = ({ to, label }: { to: string; label: string }) => (
  <li>
    <Link to={to} className="text-cream/80 hover:text-cream transition-colors">
      {label}
    </Link>
  </li>
);

// Social Link Component
const SocialLink = ({
  href,
  icon,
  label
}: {
  href: string;
  icon: React.ReactNode;
  label: string
}) => (
  <a
    href={href}
    className="bg-mocha hover:bg-cream hover:text-coffee rounded-full p-2 transition-colors"
    aria-label={label}
  >
    {icon}
  </a>
);

export default Footer;