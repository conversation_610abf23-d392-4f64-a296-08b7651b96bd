import React, { useState } from 'react';
import { motion } from 'framer-motion';
import AdminLayout from '../../components/admin/AdminLayout';
import SampleDataLoader from '../../components/admin/SampleDataLoader';
import DatabaseTest from '../../components/admin/DatabaseTest';
import MenuImporter from '../../components/admin/MenuImporter';
import { AlertTriangle, Code, Database, Upload, Trash2 } from 'lucide-react';
import { useAdminLanguage } from '../../contexts/AdminLanguageContext';

const DeveloperTools = () => {
  const { t } = useAdminLanguage();
  const [showWarning, setShowWarning] = useState<string | null>(null);
  const [pendingAction, setPendingAction] = useState<(() => void) | null>(null);

  const handleDangerousAction = (actionType: string, action: () => void) => {
    setShowWarning(actionType);
    setPendingAction(() => action);
  };

  const confirmAction = () => {
    if (pendingAction) {
      pendingAction();
    }
    setShowWarning(null);
    setPendingAction(null);
  };

  const cancelAction = () => {
    setShowWarning(null);
    setPendingAction(null);
  };

  const getWarningMessage = (actionType: string) => {
    switch (actionType) {
      case 'menuImport':
        return t('admin.menuImportTool.warning');
      case 'databaseTest':
        return t('admin.databaseTest.warning');
      case 'clearMenu':
        return t('admin.clearMenuItems.warning');
      default:
        return 'Are you sure you want to perform this action?';
    }
  };

  return (
    <AdminLayout>
      <div className="p-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-2">
            <Code className="h-8 w-8 text-coffee mr-3" />
            <h1 className="text-3xl font-bold text-coffee">{t('admin.developerTools')}</h1>
          </div>
          <p className="text-gray-600">{t('admin.developerTools.subtitle')}</p>
          
          {/* Warning Notice */}
          <div className="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start">
              <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2 mt-0.5" />
              <div>
                <h3 className="text-sm font-medium text-yellow-800">
                  {t('admin.dangerousAction')}
                </h3>
                <p className="text-sm text-yellow-700 mt-1">
                  These tools can modify your database and should be used with caution. 
                  Always backup your data before using these tools.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Tools Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          
          {/* Menu Import Tool */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-white rounded-lg shadow-md p-6"
          >
            <div className="flex items-center mb-4">
              <Upload className="h-6 w-6 text-blue-600 mr-3" />
              <h2 className="text-xl font-bold text-gray-900">{t('admin.menuImportTool')}</h2>
            </div>
            <p className="text-gray-600 mb-4">{t('admin.menuImportTool.desc')}</p>
            <MenuImporter onAction={(action) => handleDangerousAction('menuImport', action)} />
          </motion.div>

          {/* Database Test */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="bg-white rounded-lg shadow-md p-6"
          >
            <div className="flex items-center mb-4">
              <Database className="h-6 w-6 text-green-600 mr-3" />
              <h2 className="text-xl font-bold text-gray-900">{t('admin.databaseTest')}</h2>
            </div>
            <p className="text-gray-600 mb-4">{t('admin.databaseTest.desc')}</p>
            <DatabaseTest onAction={(action) => handleDangerousAction('databaseTest', action)} />
          </motion.div>

          {/* Sample Data Loader */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="bg-white rounded-lg shadow-md p-6 lg:col-span-2"
          >
            <div className="flex items-center mb-4">
              <Trash2 className="h-6 w-6 text-red-600 mr-3" />
              <h2 className="text-xl font-bold text-gray-900">{t('admin.devTools')}</h2>
            </div>
            <p className="text-gray-600 mb-4">Advanced development tools for data management</p>
            <SampleDataLoader />
          </motion.div>
        </div>

        {/* Warning Modal */}
        {showWarning && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4"
            >
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <AlertTriangle className="h-8 w-8 text-red-600 mr-3" />
                  <h3 className="text-lg font-bold text-gray-900">
                    {t('admin.dangerousAction')}
                  </h3>
                </div>
                
                <p className="text-gray-600 mb-6">
                  {getWarningMessage(showWarning)}
                </p>
                
                <div className="flex justify-end space-x-3">
                  <button
                    onClick={cancelAction}
                    className="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium"
                  >
                    {t('admin.cancelAction')}
                  </button>
                  <button
                    onClick={confirmAction}
                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 font-medium"
                  >
                    {t('admin.confirmAction')}
                  </button>
                </div>
              </div>
            </motion.div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default DeveloperTools;
