import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Calendar, User, Tag, ArrowLeft } from 'lucide-react';
import { format } from 'date-fns';
import { supabase, BlogPost } from '../lib/supabaseClient';
import SEO from '../components/SEO';
import { useLanguage } from '../contexts/LanguageContext';

const BlogPostPage = () => {
  const { id } = useParams<{ id: string }>();
  const [post, setPost] = useState<BlogPost | null>(null);
  const [loading, setLoading] = useState(true);
  const [relatedPosts, setRelatedPosts] = useState<BlogPost[]>([]);
  const { language, t } = useLanguage();

  // Helper function to get localized content
  const getLocalizedContent = (post: BlogPost) => {
    if (language === 'en') {
      return {
        title: post.title_en || post.title,
        excerpt: post.excerpt_en || post.excerpt,
        content: post.content_en || post.content
      };
    }
    return {
      title: post.title,
      excerpt: post.excerpt,
      content: post.content
    };
  };

  useEffect(() => {
    const fetchBlogPost = async () => {
      setLoading(true);

      if (!id) return;

      // Try to fetch by slug first, then by ID for backward compatibility
      let data, error;

      // Check if id looks like a UUID (for backward compatibility)
      const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);

      if (isUUID) {
        // Fetch by ID (backward compatibility)
        const result = await supabase
          .from('blog_posts')
          .select('*')
          .eq('id', id)
          .eq('published', true)
          .single();
        data = result.data;
        error = result.error;
      } else {
        // Fetch by slug (new SEO-friendly URLs)
        const result = await supabase
          .from('blog_posts')
          .select('*')
          .eq('slug', id)
          .eq('published', true)
          .single();
        data = result.data;
        error = result.error;
      }

      if (error) {
        console.error('Error fetching blog post:', error);
      } else if (data) {
        setPost(data);

        // Fetch related posts in the same category
        const { data: related, error: relatedError } = await supabase
          .from('blog_posts')
          .select('*')
          .eq('category', data.category)
          .eq('published', true)
          .neq('id', id)
          .limit(3);

        if (relatedError) {
          console.error('Error fetching related posts:', relatedError);
        } else {
          setRelatedPosts(related || []);
        }
      }

      setLoading(false);
    };

    fetchBlogPost();
  }, [id, language]); // Add language dependency

  // Default post if none from database
  const defaultPost: BlogPost = {
    id: '1',
    title: 'Sulu Burgerlerimizin Sırrı',
    title_en: 'The Secret to Our Juicy Burgers',
    content: `<p>Shebo's Burger'da şehrin en sulu, en lezzetli burgerlerini sunmaktan gurur duyuyoruz. Peki sırrımız nedir? Her şey kaliteli malzemelerle başlıyor.</p><p>Sadece her gün taze çekilmiş premium dana eti kullanıyoruz. Chuck ve brisket karışımımız, sulu ve lezzetli bir köfte için mükemmel yağ oranını sağlıyor. Köftelerimizi, dana etinin doğal lezzetini artıran özel baharat karışımımızla tatlandırıyoruz.</p><p>Burgerlerimizi nasıl pişirdiğimiz de önemli bir faktör. Köfteleri mükemmel şekilde mühürleyen, suları içinde kilitleyen ve lezzetli bir kabuk oluşturan düz ızgara kullanıyoruz. Her zaman siparişe göre pişiriyoruz, böylece her burger masanıza en lezzetli halinde ulaşıyor.</p><p>Son olarak, detaylara dikkat ediyoruz. Ekmeklerimiz her gün taze pişiriliyor ve mükemmel doku için hafifçe kızartılıyor. Çıtır maruldan olgun domateslerine kadar garnitürlerimiz her zaman taze ve yüksek kaliteli.</p><p>Shebo's Burger'da farkı tadın!</p>`,
    content_en: `<p>At Shebo's Burger, we take pride in serving the juiciest, most flavorful burgers in town. But what's our secret? It all starts with quality ingredients.</p><p>We use only premium beef that's ground fresh daily. Our special blend of chuck and brisket provides the perfect fat content for a juicy, flavorful patty. We season our patties with a proprietary mix of spices that enhances the natural flavor of the beef without overwhelming it.</p><p>Another key factor is how we cook our burgers. We use a flat-top grill that sears the patties perfectly, locking in juices and creating a delicious crust. We always cook to order, ensuring each burger reaches your table at peak deliciousness.</p><p>Finally, we pay attention to the details. Our buns are baked fresh daily, and we toast them lightly for the perfect texture. Our toppings are always fresh and high-quality, from crisp lettuce to vine-ripened tomatoes.</p><p>Come taste the difference at Shebo's Burger!</p>`,
    excerpt: 'Burgerlerimizi rakiplerinden ayıran şeyi keşfedin - malzeme seçiminden pişirme tekniklerine kadar.',
    excerpt_en: 'Discover what makes our burgers stand out from the competition - from ingredient selection to cooking techniques.',
    image_url: 'https://images.pexels.com/photos/3219547/pexels-photo-3219547.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    author: 'Şef Michael',
    category: 'Pişirme İpuçları',
    language: 'tr',
    published: true,
    created_at: '2023-06-15T12:00:00Z'
  };

  // Default related posts
  const defaultRelated: BlogPost[] = [
    {
      id: '2',
      title: 'Upcoming Summer Special Menu',
      excerpt: 'Get a sneak peek at our upcoming summer menu featuring seasonal ingredients and tropical flavors.',
      content: '',
      image_url: 'https://images.pexels.com/photos/1600711/pexels-photo-1600711.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      author: 'Sarah Williams',
      category: 'Menu Updates',
      published: true,
      created_at: '2023-05-20T14:30:00Z'
    },
    {
      id: '3',
      title: 'The History of Shebo\'s Burger',
      excerpt: 'Learn about how Shebo\'s Burger grew from a small food truck to the beloved restaurant it is today.',
      content: '',
      image_url: 'https://images.pexels.com/photos/2983101/pexels-photo-2983101.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      author: 'James Williams',
      category: 'Our Story',
      published: true,
      created_at: '2023-04-10T09:15:00Z'
    }
  ];

  // Use default if no data from database
  useEffect(() => {
    if (!loading && !post && id === '1') {
      setPost(defaultPost);
      setRelatedPosts(defaultRelated);
    }
  }, [loading, post, id]);

  if (loading) {
    return (
      <div className="pt-24 pb-12">
        <div className="container-custom">
          <div className="text-center py-12">
            <p className="text-xl">Loading blog post...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="pt-24 pb-12">
        <div className="container-custom">
          <div className="text-center py-12">
            <h2 className="text-3xl font-bold mb-4">Blog Post Not Found</h2>
            <p className="mb-6">The blog post you're looking for doesn't exist or has been removed.</p>
            <Link to="/blog" className="btn btn-primary">
              Back to Blog
            </Link>
          </div>
        </div>
      </div>
    );
  }

  const formattedDate = format(new Date(post.created_at), 'MMMM d, yyyy');
  const localizedContent = getLocalizedContent(post);

  return (
    <div className="pt-16">
      <SEO
        title={`${localizedContent.title} - Shebo's Burger Blog`}
        description={localizedContent.excerpt}
        keywords={`${post.category}, blog, ${localizedContent.title}, Shebo's Burger`}
        image={post.image_url}
        type="article"
      />

      {/* Hero Image */}
      <section
        className="relative h-[50vh] bg-cover bg-center"
        style={{
          backgroundImage: `linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), url(${post.image_url})`
        }}
      >
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="container-custom text-center text-white">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h1 className="text-4xl md:text-5xl font-serif font-bold mb-4 text-white">
                {localizedContent.title}
              </h1>
              <div className="flex flex-wrap justify-center gap-6 mb-3">
                <span className="flex items-center text-sm">
                  <Calendar className="h-4 w-4 mr-1" />
                  {formattedDate}
                </span>
                <span className="flex items-center text-sm">
                  <User className="h-4 w-4 mr-1" />
                  {post.author}
                </span>
                <span className="flex items-center text-sm">
                  <Tag className="h-4 w-4 mr-1" />
                  {post.category}
                </span>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Blog Content */}
      <section className="py-12">
        <div className="container-custom">
          <div className="max-w-3xl mx-auto">
            <Link to="/blog" className="inline-flex items-center text-coffee hover:underline mb-8">
              <ArrowLeft className="h-4 w-4 mr-1" />
              {t('blog.title')}
            </Link>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              className="bg-white p-8 rounded-lg shadow-md"
            >
              <div
                className="prose prose-coffee max-w-none"
                dangerouslySetInnerHTML={{ __html: localizedContent.content }}
              />

              {/* Social Share */}
              <div className="mt-8 pt-6 border-t border-gray-200">
                <h4 className="font-bold mb-3">{t('blog.shareOn')}</h4>
                <div className="flex space-x-4">
                  <a
                    href={`https://twitter.com/intent/tweet?text=${encodeURIComponent(post.title)}&url=${encodeURIComponent(window.location.href)}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-[#1DA1F2] text-white p-2 rounded-full"
                  >
                    <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" />
                    </svg>
                  </a>
                  <a
                    href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-[#4267B2] text-white p-2 rounded-full"
                  >
                    <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                    </svg>
                  </a>
                  <a
                    href={`https://www.linkedin.com/shareArticle?mini=true&url=${encodeURIComponent(window.location.href)}&title=${encodeURIComponent(post.title)}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-[#0A66C2] text-white p-2 rounded-full"
                  >
                    <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                    </svg>
                  </a>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Related Posts */}
      {relatedPosts.length > 0 && (
        <section className="py-12 bg-cream">
          <div className="container-custom">
            <h2 className="text-3xl font-serif font-bold mb-8">Related Posts</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {relatedPosts.map((relatedPost) => (
                <RelatedPostCard key={relatedPost.id} post={relatedPost} />
              ))}
            </div>
          </div>
        </section>
      )}
    </div>
  );
};

// Related Post Card Component
const RelatedPostCard = ({ post }: { post: BlogPost }) => {
  const { language } = useLanguage();

  // Helper function to get localized content for related posts
  const getLocalizedContent = (post: BlogPost) => {
    if (language === 'en') {
      return {
        title: post.title_en || post.title,
        excerpt: post.excerpt_en || post.excerpt
      };
    }
    return {
      title: post.title,
      excerpt: post.excerpt
    };
  };

  const localizedContent = getLocalizedContent(post);

  return (
    <motion.article
      className="blog-card h-full"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.4 }}
    >
      <Link to={`/blog/${post.slug || post.id}`} className="block h-full">
        <div className="h-48 overflow-hidden">
          <img
            src={post.image_url || 'https://images.pexels.com/photos/1855214/pexels-photo-1855214.jpeg'}
            alt={localizedContent.title}
            className="w-full h-full object-cover"
          />
        </div>

        <div className="p-4">
          <span className="text-sm text-coffee font-medium">{post.category}</span>
          <h3 className="font-bold text-xl mb-2">{localizedContent.title}</h3>
          <p className="text-gray-600 text-sm line-clamp-2">{localizedContent.excerpt}</p>
        </div>
      </Link>
    </motion.article>
  );
};

export default BlogPostPage;