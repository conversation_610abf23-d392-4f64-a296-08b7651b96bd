import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Lock, Mail, Eye, EyeOff, ArrowLeft } from 'lucide-react';
import { supabase } from '../lib/supabaseClient';
import { useLanguage } from '../contexts/LanguageContext';

const Login = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [mode, setMode] = useState<'login' | 'forgot'>('login');
  const navigate = useNavigate();
  const { t } = useLanguage();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      navigate('/admin');
    } catch (error: any) {
      setError(error.message || t('login.loginError'));
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/admin/reset-password`,
      });

      if (error) throw error;

      setSuccess(t('login.resetSuccess'));
    } catch (error: any) {
      setError(error.message || t('login.loginError'));
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setError(null);
    setSuccess(null);
    setEmail('');
    setPassword('');
  };

  return (
    <div className="min-h-screen pt-16 pb-12 flex items-center justify-center bg-cream">
      <div className="w-full max-w-md">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white rounded-lg shadow-xl overflow-hidden"
        >
          <div className="bg-coffee p-6 flex justify-center">
            <div className="text-center">
              <div className="inline-flex items-center justify-center bg-cream text-coffee p-3 rounded-full mb-3">
                {mode === 'login' ? <Lock className="h-6 w-6" /> : <Mail className="h-6 w-6" />}
              </div>
              <h2 className="text-2xl font-bold text-white">
                {mode === 'login' ? t('login.title') : t('login.resetPassword')}
              </h2>
              <p className="text-cream/80 mt-1 text-sm">
                {mode === 'login'
                  ? t('login.subtitle')
                  : t('login.resetInstructions')
                }
              </p>
            </div>
          </div>
          
          <div className="p-8">
            {error && (
              <motion.div
                className="mb-4 p-3 bg-red-100 text-red-800 rounded-md"
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3 }}
              >
                {error}
              </motion.div>
            )}

            {success && (
              <motion.div
                className="mb-4 p-3 bg-green-100 text-green-800 rounded-md"
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3 }}
              >
                {success}
              </motion.div>
            )}
            
            <form onSubmit={mode === 'login' ? handleLogin : handleForgotPassword}>
              <div className="mb-6">
                <label htmlFor="email" className="form-label">
                  {t('login.email')}
                </label>
                <input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="form-input"
                  placeholder={t('login.email')}
                  required
                />
              </div>

              {mode === 'login' && (
                <div className="mb-6">
                  <label htmlFor="password" className="form-label">
                    {t('login.password')}
                  </label>
                  <div className="relative">
                    <input
                      id="password"
                      type={showPassword ? 'text' : 'password'}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="form-input pr-10"
                      placeholder={t('login.password')}
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                      title={showPassword ? t('login.hidePassword') : t('login.showPassword')}
                    >
                      {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    </button>
                  </div>
                </div>
              )}

              <button
                type="submit"
                className="w-full btn btn-primary flex justify-center items-center"
                disabled={loading}
              >
                {loading
                  ? (mode === 'login' ? t('login.signingIn') : t('login.sendingEmail'))
                  : (mode === 'login' ? t('login.signIn') : t('login.resetPassword'))
                }
              </button>
            </form>

            <div className="mt-6 text-center">
              {mode === 'login' ? (
                <button
                  type="button"
                  onClick={() => {
                    setMode('forgot');
                    resetForm();
                  }}
                  className="text-coffee hover:text-mocha text-sm font-medium"
                >
                  {t('login.forgotPassword')}
                </button>
              ) : (
                <button
                  type="button"
                  onClick={() => {
                    setMode('login');
                    resetForm();
                  }}
                  className="text-coffee hover:text-mocha text-sm font-medium flex items-center justify-center mx-auto"
                >
                  <ArrowLeft className="h-4 w-4 mr-1" />
                  {t('login.backToLogin')}
                </button>
              )}
            </div>

          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Login;