-- Add multi-language support to existing tables
-- Run this in your Supabase SQL Editor
-- This script only adds language columns and copies existing content, no new data

-- Step 1: Add language columns to blog_posts table
DO $$
BEGIN
    -- Add language column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'blog_posts' AND column_name = 'language') THEN
        ALTER TABLE blog_posts ADD COLUMN language VARCHAR(2) DEFAULT 'tr';
    END IF;

    -- Add title_en column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'blog_posts' AND column_name = 'title_en') THEN
        ALTER TABLE blog_posts ADD COLUMN title_en TEXT;
    END IF;

    -- Add excerpt_en column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'blog_posts' AND column_name = 'excerpt_en') THEN
        ALTER TABLE blog_posts ADD COLUMN excerpt_en TEXT;
    END IF;

    -- Add content_en column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'blog_posts' AND column_name = 'content_en') THEN
        ALTER TABLE blog_posts ADD COLUMN content_en TEXT;
    END IF;
END $$;

-- Step 2: Add language columns to menu_items table
DO $$
BEGIN
    -- Add language column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'menu_items' AND column_name = 'language') THEN
        ALTER TABLE menu_items ADD COLUMN language VARCHAR(2) DEFAULT 'tr';
    END IF;

    -- Add name_en column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'menu_items' AND column_name = 'name_en') THEN
        ALTER TABLE menu_items ADD COLUMN name_en TEXT;
    END IF;

    -- Add description_en column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'menu_items' AND column_name = 'description_en') THEN
        ALTER TABLE menu_items ADD COLUMN description_en TEXT;
    END IF;
END $$;

-- Step 3: Create content table if it doesn't exist
CREATE TABLE IF NOT EXISTS content (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    image_url TEXT,
    display_order INTEGER DEFAULT 0,
    section VARCHAR(255) NOT NULL,
    page VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 4: Add language columns to content table
DO $$
BEGIN
    -- Add language column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'content' AND column_name = 'language') THEN
        ALTER TABLE content ADD COLUMN language VARCHAR(2) DEFAULT 'tr';
    END IF;

    -- Add title_en column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'content' AND column_name = 'title_en') THEN
        ALTER TABLE content ADD COLUMN title_en TEXT;
    END IF;

    -- Add content_en column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'content' AND column_name = 'content_en') THEN
        ALTER TABLE content ADD COLUMN content_en TEXT;
    END IF;
END $$;

-- Step 5: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_blog_posts_language ON blog_posts(language);
CREATE INDEX IF NOT EXISTS idx_menu_items_language ON menu_items(language);
CREATE INDEX IF NOT EXISTS idx_content_language ON content(language);
CREATE INDEX IF NOT EXISTS idx_content_page_section ON content(page, section);
CREATE INDEX IF NOT EXISTS idx_menu_items_category ON menu_items(category);
CREATE INDEX IF NOT EXISTS idx_menu_items_type ON menu_items(menu_type);

-- Step 6: Update existing data to have Turkish as default language
UPDATE blog_posts SET language = 'tr' WHERE language IS NULL OR language = '';
UPDATE menu_items SET language = 'tr' WHERE language IS NULL OR language = '';
UPDATE content SET language = 'tr' WHERE language IS NULL OR language = '';

-- Step 7: Copy existing Turkish content to English fields as starting point
-- This gives you a base to work with - you can edit these through the admin panel

UPDATE blog_posts SET
    title_en = title,
    excerpt_en = excerpt,
    content_en = content
WHERE (title_en IS NULL OR title_en = '') AND title IS NOT NULL;

UPDATE menu_items SET
    name_en = name,
    description_en = description
WHERE (name_en IS NULL OR name_en = '') AND name IS NOT NULL;

UPDATE content SET
    title_en = title,
    content_en = content
WHERE (title_en IS NULL OR title_en = '') AND title IS NOT NULL;

-- Step 8: Show summary of what was done
SELECT
    'blog_posts' as table_name,
    COUNT(*) as total_rows,
    COUNT(CASE WHEN title_en IS NOT NULL AND title_en != '' THEN 1 END) as rows_with_english
FROM blog_posts
UNION ALL
SELECT
    'menu_items' as table_name,
    COUNT(*) as total_rows,
    COUNT(CASE WHEN name_en IS NOT NULL AND name_en != '' THEN 1 END) as rows_with_english
FROM menu_items
UNION ALL
SELECT
    'content' as table_name,
    COUNT(*) as total_rows,
    COUNT(CASE WHEN title_en IS NOT NULL AND title_en != '' THEN 1 END) as rows_with_english
FROM content;
