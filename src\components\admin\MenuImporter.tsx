import { useState } from 'react';
import { Upload, Download, AlertTriangle, CheckCircle } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';
import { importMenuItems, clearMenuItems } from '../../utils/menuImport';

interface MenuImporterProps {
  onAction?: (action: () => void) => void;
}

const MenuImporter = ({ onAction }: MenuImporterProps) => {
  const { t } = useLanguage();
  const [importing, setImporting] = useState(false);
  const [clearing, setClearing] = useState(false);
  const [result, setResult] = useState<{ success: boolean; message: string; count?: number } | null>(null);

  const handleImport = async () => {
    setImporting(true);
    setResult(null);
    
    try {
      const result = await importMenuItems();
      
      if (result.success) {
        setResult({
          success: true,
          message: `Successfully imported ${result.count} menu items!`,
          count: result.count
        });
      } else {
        setResult({
          success: false,
          message: `Import failed: ${result.error}`
        });
      }
    } catch (error) {
      setResult({
        success: false,
        message: `Import failed: ${error.message}`
      });
    } finally {
      setImporting(false);
    }
  };

  const handleClear = async () => {
    setClearing(true);
    setResult(null);
    
    try {
      const result = await clearMenuItems();
      
      if (result.success) {
        setResult({
          success: true,
          message: 'All menu items cleared successfully!'
        });
      } else {
        setResult({
          success: false,
          message: `Clear failed: ${result.error}`
        });
      }
    } catch (error) {
      setResult({
        success: false,
        message: `Clear failed: ${error.message}`
      });
    } finally {
      setClearing(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-bold mb-4 flex items-center">
        <Upload className="h-5 w-5 mr-2" />
        Menu Import Tool
      </h3>
      
      <div className="space-y-4">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-semibold text-blue-800 mb-2">Import Complete Menu</h4>
          <p className="text-blue-700 text-sm mb-3">
            This will import 47 menu items including:
          </p>
          <ul className="text-blue-700 text-sm space-y-1 mb-4">
            <li>• <strong>Cafe Menu:</strong> Hot/Cold Coffees, Teas, Snacks (26 items)</li>
            <li>• <strong>Restaurant Menu:</strong> Burgers, Chicken, Hot Dogs (21 items)</li>
            <li>• <strong>Features:</strong> Turkish/English names, proper categories, pricing</li>
          </ul>
          
          <button
            onClick={() => onAction ? onAction(handleImport) : handleImport()}
            disabled={importing}
            className="btn bg-green-600 text-white hover:bg-green-700 disabled:opacity-50 flex items-center"
          >
            {importing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Importing...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Import Menu Items
              </>
            )}
          </button>
        </div>

        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h4 className="font-semibold text-red-800 mb-2 flex items-center">
            <AlertTriangle className="h-4 w-4 mr-2" />
            Clear All Menu Items
          </h4>
          <p className="text-red-700 text-sm mb-3">
            ⚠️ This will permanently delete ALL existing menu items. Use with caution!
          </p>
          
          <button
            onClick={() => onAction ? onAction(handleClear) : handleClear()}
            disabled={clearing}
            className="btn bg-red-600 text-white hover:bg-red-700 disabled:opacity-50 flex items-center"
          >
            {clearing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Clearing...
              </>
            ) : (
              <>
                <AlertTriangle className="h-4 w-4 mr-2" />
                Clear All Items
              </>
            )}
          </button>
        </div>

        {result && (
          <div className={`border rounded-lg p-4 ${
            result.success 
              ? 'bg-green-50 border-green-200' 
              : 'bg-red-50 border-red-200'
          }`}>
            <div className="flex items-center">
              {result.success ? (
                <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
              ) : (
                <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
              )}
              <span className={`font-medium ${
                result.success ? 'text-green-800' : 'text-red-800'
              }`}>
                {result.message}
              </span>
            </div>
          </div>
        )}

        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h4 className="font-semibold text-gray-800 mb-2">Manual Import Options</h4>
          <p className="text-gray-700 text-sm mb-2">
            You can also import manually using:
          </p>
          <ul className="text-gray-700 text-sm space-y-1">
            <li>• <strong>SQL File:</strong> Use <code>menu-import.sql</code> in Supabase Dashboard</li>
            <li>• <strong>Database Test:</strong> Copy SQL queries to the Database Test section below</li>
            <li>• <strong>Admin Panel:</strong> Add items individually through Menu Management</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default MenuImporter;
