import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';

export type UserRole = 'admin' | 'editor' | null;

export interface UserPermissions {
  canManageUsers: boolean;
  canManageSettings: boolean;
  canManageMenu: boolean;
  canManageBlog: boolean;
  canManageContent: boolean;
  canViewContactSubmissions: boolean;
  canDeleteContent: boolean;
  canPublishContent: boolean;
}

export const useUserRole = () => {
  const { session } = useAuth();
  const [userRole, setUserRole] = useState<UserRole>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (session?.user) {
      // Get role from user metadata
      const role = session.user.user_metadata?.role || 'admin'; // Default to admin for existing users
      setUserRole(role as UserRole);
    } else {
      setUserRole(null);
    }
    setLoading(false);
  }, [session]);

  const getPermissions = (): UserPermissions => {
    if (!userRole) {
      return {
        canManageUsers: false,
        canManageSettings: false,
        canManageMenu: false,
        canManageBlog: false,
        canManageContent: false,
        canViewContactSubmissions: false,
        canDeleteContent: false,
        canPublishContent: false,
      };
    }

    if (userRole === 'admin') {
      // Admin has all permissions
      return {
        canManageUsers: true,
        canManageSettings: true,
        canManageMenu: true,
        canManageBlog: true,
        canManageContent: true,
        canViewContactSubmissions: true,
        canDeleteContent: true,
        canPublishContent: true,
      };
    }

    if (userRole === 'editor') {
      // Editor has limited permissions
      return {
        canManageUsers: false,
        canManageSettings: false,
        canManageMenu: true,
        canManageBlog: true,
        canManageContent: true,
        canViewContactSubmissions: true,
        canDeleteContent: false, // Editors cannot delete
        canPublishContent: false, // Editors cannot publish (only draft)
      };
    }

    return {
      canManageUsers: false,
      canManageSettings: false,
      canManageMenu: false,
      canManageBlog: false,
      canManageContent: false,
      canViewContactSubmissions: false,
      canDeleteContent: false,
      canPublishContent: false,
    };
  };

  const hasPermission = (permission: keyof UserPermissions): boolean => {
    const permissions = getPermissions();
    return permissions[permission];
  };

  const isAdmin = (): boolean => userRole === 'admin';
  const isEditor = (): boolean => userRole === 'editor';

  return {
    userRole,
    loading,
    permissions: getPermissions(),
    hasPermission,
    isAdmin,
    isEditor,
  };
};
